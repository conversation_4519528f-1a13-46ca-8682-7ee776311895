"use client";
import React, { useState } from "react";
import Lightbox from "yet-another-react-lightbox";
import "yet-another-react-lightbox/styles.css";
import Video from "yet-another-react-lightbox/plugins/video";
import Thumbnails from "yet-another-react-lightbox/plugins/thumbnails";
import "yet-another-react-lightbox/plugins/thumbnails.css";
import Zoom from "yet-another-react-lightbox/plugins/zoom";
import RichTextFormatter from "@/components/RichTextFormatter";
import { CustomizationOption } from "../../types/basket"; // Import from types
import { generateFileUrl } from "@/lib/utils";

interface ViewServiceProps {
  onSelectService: (id: number) => void;
  id: string;
  custamizationId: string;
  currencySymbol?: string;
  selectedOption: CustomizationOption | null;
}

const CustomizationDetails: React.FC<ViewServiceProps> = ({
  onSelectService,
  custamizationId,
  currencySymbol = "£",
  selectedOption,
}) => {
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [lightboxSlides, setLightboxSlides] = useState<any[]>([]);

  // Helper function to determine if media item is a video
  const isVideoMedia = (mediaItem: any): boolean => {
    if (typeof mediaItem === "string") {
      return (
        mediaItem.includes("videos") || mediaItem.includes(".mp4") || mediaItem.includes(".webm")
      );
    }
    return mediaItem?.type === "videos" || mediaItem?.type === "video";
  };

  // Helper function to get media URL
  const getMediaUrl = (mediaItem: any): string => {
    if (typeof mediaItem === "string") {
      return mediaItem;
    }
    return mediaItem?.url || mediaItem?.src || mediaItem;
  };

  // const generateFileUrl = (postFile: string | undefined): string | undefined => {
  //   const baseUrl = process.env.BASE_STORAGE_URL;
  //   if (!baseUrl) return undefined;

  //   if (!postFile) {
  //     return undefined;
  //   }

  //   if (postFile.startsWith("https://firebasestorage.googleapis.com/")) {
  //     return postFile;
  //   }

  //   return `${baseUrl}${encodeURIComponent(postFile)}?alt=media`;
  // };
  // Prepare lightbox slides from media array
  const prepareLightboxSlides = (mediaArray: any[]) => {
    return mediaArray.map((mediaItem: any) => {
      const mediaUrl = generateFileUrl(getMediaUrl(mediaItem));
      const isVideo = isVideoMedia(mediaItem);

      if (isVideo) {
        return {
          type: "video",
          width: 1280,
          height: 720,
          controls: true,
          muted: false,
          autoPlay: false,
          poster: mediaUrl,
          sources: [
            {
              src: mediaUrl,
              type: "video/mp4",
            },
          ],
        };
      } else {
        return {
          src: mediaUrl,
          width: 1280,
          height: 720,
        };
      }
    });
  };

  // Handle lightbox open
  const handleLightboxOpen = (mediaArray: any[], clickedIndex: number) => {
    const slides = prepareLightboxSlides(mediaArray);
    setLightboxSlides(slides);
    setLightboxIndex(clickedIndex);
    setLightboxOpen(true);
  };

  if (!selectedOption) {
    return <div className="p-4 text-gray-500">No details available for this customization.</div>;
  }

  return (
    <>
      <div>
        <div className="bg-white">
          <div className="flex flex-col w-full gap-3  bg-white pb-16 px-3 max-md:px-2">
            <div>
              <p className="my-3 text-primary text-xl font-bold">{selectedOption.title}</p>
              <RichTextFormatter
                text={selectedOption.description || ""}
                className="mt-2 text-subtitle"
                preserveWhitespace={true}
                enableMarkdown={true}
              />

              <div className="mt-2">
                <p className="text-subtitle">
                  Price: {currencySymbol}
                  {selectedOption.price
                    ? (parseFloat(selectedOption.price) / (1 - 0.16)).toFixed(2)
                    : "0.00"}
                </p>
              </div>

              <div className="mt-3">
                {selectedOption.media && selectedOption.media.length > 0 ? (
                  <div>
                    <div className="grid grid-cols-2 gap-3">
                      {selectedOption.media.map((mediaItem: any, mediaIndex: number) => {
                        const isVideo = isVideoMedia(mediaItem);
                        const mediaUrl = getMediaUrl(mediaItem);

                        return (
                          <div
                            key={mediaIndex}
                            className="relative rounded-md overflow-hidden h-32 border border-gray-200 bg-gray-100"
                          >
                            {mediaUrl ? (
                              <img
                                // src={generateFileUrlmediaUrl}
                                src={generateFileUrl(mediaUrl) || "/assets/noimg.png"}
                                alt={`Media ${mediaIndex}`}
                                className="w-full object-cover cursor-pointer h-32"
                                onClick={() => handleLightboxOpen(selectedOption.media, mediaIndex)}
                                onError={(e) => {
                                  console.error(`Failed to load image: ${mediaUrl}`);
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = "none";
                                }}
                                onLoad={() => {
                                  console.log(`Successfully loaded image: ${mediaUrl}`);
                                }}
                              />
                            ) : (
                              <div className="w-full h-32 bg-red-100 flex items-center justify-center">
                                <p className="text-xs text-red-600">No URL</p>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ) : (
                  <div>
                    <p className="text-gray-500">No media available</p>
                    {/* <p className="text-xs text-gray-400 mt-1">
                      selectedOption.media: {selectedOption.media ? "exists" : "null/undefined"}
                    </p>
                    <p className="text-xs text-gray-400">
                      length: {selectedOption.media?.length || "N/A"}
                    </p> */}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Lightbox with Thumbnails, Zoom, and Navigation */}
      <Lightbox
        open={lightboxOpen}
        close={() => setLightboxOpen(false)}
        index={lightboxIndex}
        slides={lightboxSlides}
        plugins={[Video, Thumbnails, Zoom]}
        carousel={{
          preload: 2,
          finite: true,
          spacing: 0,
          padding: 0,
        }}
        toolbar={{ buttons: ["close"] }}
        controller={{
          closeOnBackdropClick: true,
          touchAction: "none",
        }}
        thumbnails={{
          position: "bottom",
          width: 120,
          height: 80,
          border: 2,
          borderRadius: 4,
          padding: 4,
          gap: 16,
          imageFit: "cover",
          vignette: false,
        }}
        render={{
          thumbnail: ({ slide, rect }) => {
            const isVideo = slide.type === "video";
            const src = isVideo ? slide.poster || slide.sources?.[0]?.src : slide.src;

            return (
              <div
                style={{
                  width: rect.width,
                  height: rect.height,
                  overflow: "hidden",
                  borderRadius: "4px",
                  backgroundColor: "#f3f4f6",
                  zIndex: 55,
                }}
              >
                {isVideo ? (
                  <video
                    src={src}
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    muted
                    preload="metadata"
                    crossOrigin="anonymous"
                  />
                ) : (
                  <img
                    src={src}
                    alt="Thumbnail"
                    style={{
                      width: "100%",
                      height: "100%",
                      objectFit: "cover",
                    }}
                    loading="lazy"
                  />
                )}
              </div>
            );
          },
        }}
        zoom={{
          maxZoomPixelRatio: 3,
          zoomInMultiplier: 2,
          doubleClickMaxStops: 3,
          keyboardMoveDistance: 50,
          wheelZoomDistanceFactor: 100,
          pinchZoomDistanceFactor: 100,
          scrollToZoom: true,
        }}
        styles={{
          container: {
            background: "rgba(0, 0, 0, 0.8)",
          },
          thumbnailsContainer: {
            background: "rgba(0, 0, 0, 0.8)",
          },
          thumbnail: {
            border: "2px solid transparent",
          },
          thumbnailsTrack: {
            padding: "16px",
          },
        }}
      />

      <style>
        {`
          .yarl__video_container video {
            max-width: max-content !important;
          }
          .yarl__thumbnails_thumbnail_active {
            border-color: #3b82f6 !important;
          }
          .yarl__thumbnails_thumbnail {
            background-color: #f3f4f6 !important;
            overflow: hidden !important;
          }
          .yarl__thumbnails_thumbnail img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
          }
          .yarl__thumbnails_thumbnail video {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
            display: block !important;
          }
          .yarl__navigation_prev,
          .yarl__navigation_next {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            background: rgba(0, 0, 0, 0.5) !important;
            color: white !important;
            border: none !important;
            width: 44px !important;
            height: 44px !important;
            border-radius: 50% !important;
            margin: 16px !important;
            cursor: pointer !important;
            transition: background-color 0.2s ease !important;
          }
          .yarl__navigation_prev:hover,
          .yarl__navigation_next:hover {
            background: rgba(0, 0, 0, 0.7) !important;
          }
          .yarl__navigation_prev:disabled,
          .yarl__navigation_next:disabled {
            opacity: 0.3 !important;
            cursor: not-allowed !important;
          }
        `}
      </style>
    </>
  );
};

export default CustomizationDetails;
